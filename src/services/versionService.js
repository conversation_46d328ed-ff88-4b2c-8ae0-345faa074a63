/**
 * 版本检测服务
 * 用于检查当前应用版本与服务器最新版本的差异
 */

class VersionService {
  constructor() {
    this.currentVersion = null;
    this.latestVersion = null;
    this.checkInterval = null;
    this.isChecking = false;
    this.callbacks = new Set();
    
    // 配置选项
    this.options = {
      checkIntervalMs: 1 * 60 * 1000, // 1分钟检查一次
      versionEndpoint: '/version.json', // 版本信息接口
      enableVisibilityCheck: true, // 页面可见性检查
      enableStorageCheck: true, // 本地存储检查
      maxRetries: 3, // 最大重试次数
      retryDelay: 5000, // 重试延迟
    };
    
    this.init();
  }

  /**
   * 初始化版本服务
   */
  async init() {
    try {
      // 获取当前版本
      await this.getCurrentVersion();
      
      // 立即检查一次版本
      await this.checkVersion();
      
      // 启动定时检查
      this.startPeriodicCheck();
      
      // 监听页面可见性变化
      if (this.options.enableVisibilityCheck) {
        this.setupVisibilityListener();
      }
      
      // 监听存储变化（多标签页同步）
      if (this.options.enableStorageCheck) {
        this.setupStorageListener();
      }
      
      console.log('[VersionService] 版本检测服务已启动');
    } catch (error) {
      console.error('[VersionService] 初始化失败:', error);
    }
  }

  /**
   * 获取当前版本信息
   */
  async getCurrentVersion() {
    try {
      // 尝试从构建时生成的版本文件获取
      const response = await fetch('/version.json', {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
      
      if (response.ok) {
        const versionData = await response.json();
        this.currentVersion = versionData;
        console.log('[VersionService] 当前版本:', this.currentVersion);
        return this.currentVersion;
      }
    } catch (error) {
      console.warn('[VersionService] 无法获取版本文件，使用默认版本');
    }
    
    // 如果无法获取版本文件，使用默认版本信息
    this.currentVersion = {
      version: '1.0.0',
      buildTime: Date.now(),
      hash: this.generateHash()
    };
    
    return this.currentVersion;
  }

  /**
   * 检查版本更新
   */
  async checkVersion() {
    if (this.isChecking) {
      return;
    }
    
    this.isChecking = true;
    
    try {
      const response = await fetch('/version.json', {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
      
      if (response.ok) {
        const latestVersion = await response.json();
        
        // 检查是否有新版本
        if (this.hasNewVersion(this.currentVersion, latestVersion)) {
          this.latestVersion = latestVersion;
          console.log('[VersionService] 检测到新版本:', latestVersion);
          
          // 通知所有监听器
          this.notifyCallbacks({
            type: 'newVersion',
            currentVersion: this.currentVersion,
            latestVersion: this.latestVersion
          });
          
          // 存储到本地，用于多标签页同步
          if (this.options.enableStorageCheck) {
            localStorage.setItem('app_version_update', JSON.stringify({
              timestamp: Date.now(),
              latestVersion: this.latestVersion
            }));
          }
        }
      }
    } catch (error) {
      console.error('[VersionService] 版本检查失败:', error);
    } finally {
      this.isChecking = false;
    }
  }

  /**
   * 判断是否有新版本
   */
  hasNewVersion(current, latest) {
    if (!current || !latest) {
      return false;
    }
    
    // 比较构建时间
    if (latest.buildTime && current.buildTime) {
      return latest.buildTime > current.buildTime;
    }
    
    // 比较哈希值
    if (latest.hash && current.hash) {
      return latest.hash !== current.hash;
    }
    
    // 比较版本号
    if (latest.version && current.version) {
      return this.compareVersions(latest.version, current.version) > 0;
    }
    
    return false;
  }

  /**
   * 版本号比较
   */
  compareVersions(version1, version2) {
    const v1Parts = version1.split('.').map(Number);
    const v2Parts = version2.split('.').map(Number);
    const maxLength = Math.max(v1Parts.length, v2Parts.length);
    
    for (let i = 0; i < maxLength; i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;
      
      if (v1Part > v2Part) return 1;
      if (v1Part < v2Part) return -1;
    }
    
    return 0;
  }

  /**
   * 启动定时检查
   */
  startPeriodicCheck() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }
    
    this.checkInterval = setInterval(() => {
      this.checkVersion();
    }, this.options.checkIntervalMs);
  }

  /**
   * 停止定时检查
   */
  stopPeriodicCheck() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  /**
   * 设置页面可见性监听
   */
  setupVisibilityListener() {
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        // 页面变为可见时检查版本
        setTimeout(() => {
          this.checkVersion();
        }, 1000);
      }
    });
  }

  /**
   * 设置存储监听（多标签页同步）
   */
  setupStorageListener() {
    window.addEventListener('storage', (event) => {
      if (event.key === 'app_version_update' && event.newValue) {
        try {
          const data = JSON.parse(event.newValue);
          if (data.latestVersion && this.hasNewVersion(this.currentVersion, data.latestVersion)) {
            this.latestVersion = data.latestVersion;
            this.notifyCallbacks({
              type: 'newVersion',
              currentVersion: this.currentVersion,
              latestVersion: this.latestVersion,
              fromStorage: true
            });
          }
        } catch (error) {
          console.error('[VersionService] 存储事件解析失败:', error);
        }
      }
    });
  }

  /**
   * 添加版本更新回调
   */
  onVersionUpdate(callback) {
    if (typeof callback === 'function') {
      this.callbacks.add(callback);
    }
    
    // 返回取消监听的函数
    return () => {
      this.callbacks.delete(callback);
    };
  }

  /**
   * 通知所有回调
   */
  notifyCallbacks(data) {
    this.callbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('[VersionService] 回调执行失败:', error);
      }
    });
  }

  /**
   * 生成简单哈希
   */
  generateHash() {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  /**
   * 强制刷新页面
   */
  forceRefresh() {
    // 清除缓存并刷新
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistrations().then(registrations => {
        registrations.forEach(registration => {
          registration.unregister();
        });
        window.location.reload(true);
      });
    } else {
      window.location.reload(true);
    }
  }

  /**
   * 销毁服务
   */
  destroy() {
    this.stopPeriodicCheck();
    this.callbacks.clear();
    console.log('[VersionService] 版本检测服务已销毁');
  }
}

// 创建单例实例
const versionService = new VersionService();

export default versionService;
