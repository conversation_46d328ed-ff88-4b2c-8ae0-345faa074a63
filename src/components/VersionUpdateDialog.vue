<template>
  <div v-if="visible" class="version-update-overlay" @click="handleOverlayClick">
    <div class="version-update-dialog" @click.stop>
      <!-- 头部图标 -->
      <div class="dialog-header">
        <div class="update-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" 
                  fill="currentColor"/>
            <path d="M19 15L20.09 18.26L23 19L20.09 19.74L19 23L17.91 19.74L15 19L17.91 18.26L19 15Z" 
                  fill="currentColor"/>
            <path d="M5 15L6.09 18.26L9 19L6.09 19.74L5 23L3.91 19.74L1 19L3.91 18.26L5 15Z" 
                  fill="currentColor"/>
          </svg>
        </div>
      </div>

      <!-- 主要内容 -->
      <div class="dialog-content">
        <h3 class="dialog-title">发现新版本</h3>
        <p class="dialog-message">
          {{ message || '检测到应用有新版本可用，建议立即更新以获得最佳体验和最新功能。' }}
        </p>
        
        <!-- 版本信息 -->
        <div v-if="showVersionInfo" class="version-info">
          <div class="version-item">
            <span class="version-label">当前版本:</span>
            <span class="version-value">{{ currentVersion?.version || 'Unknown' }}</span>
          </div>
          <div class="version-item">
            <span class="version-label">最新版本:</span>
            <span class="version-value">{{ latestVersion?.version || 'Unknown' }}</span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="dialog-actions">
        <button 
          v-if="showLaterButton"
          class="btn btn-secondary" 
          @click="handleLater"
          :disabled="isRefreshing"
        >
          稍后提醒
        </button>
        <button 
          class="btn btn-primary" 
          @click="handleRefresh"
          :disabled="isRefreshing"
        >
          <span v-if="isRefreshing" class="loading-spinner"></span>
          {{ isRefreshing ? '正在刷新...' : '立即更新' }}
        </button>
      </div>

      <!-- 关闭按钮 -->
      <button 
        v-if="showCloseButton"
        class="close-button" 
        @click="handleClose"
        :disabled="isRefreshing"
      >
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" 
                stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'

export default {
  name: 'VersionUpdateDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentVersion: {
      type: Object,
      default: null
    },
    latestVersion: {
      type: Object,
      default: null
    },
    message: {
      type: String,
      default: ''
    },
    showVersionInfo: {
      type: Boolean,
      default: true
    },
    showLaterButton: {
      type: Boolean,
      default: true
    },
    showCloseButton: {
      type: Boolean,
      default: true
    },
    autoRefreshDelay: {
      type: Number,
      default: 0 // 0表示不自动刷新
    },
    preventClose: {
      type: Boolean,
      default: false // 是否阻止关闭对话框
    }
  },
  emits: ['refresh', 'later', 'close'],
  setup(props, { emit }) {
    const isRefreshing = ref(false)
    const autoRefreshTimer = ref(null)
    const countdown = ref(0)

    // 处理刷新
    const handleRefresh = async () => {
      if (isRefreshing.value) return
      
      isRefreshing.value = true
      
      try {
        emit('refresh')
        
        // 延迟一下再刷新，让用户看到反馈
        setTimeout(() => {
          window.location.reload(true)
        }, 500)
      } catch (error) {
        console.error('刷新失败:', error)
        isRefreshing.value = false
      }
    }

    // 处理稍后提醒
    const handleLater = () => {
      if (isRefreshing.value) return
      emit('later')
    }

    // 处理关闭
    const handleClose = () => {
      if (isRefreshing.value || props.preventClose) return
      emit('close')
    }

    // 处理遮罩点击
    const handleOverlayClick = () => {
      if (!props.preventClose) {
        handleClose()
      }
    }

    // 键盘事件处理
    const handleKeydown = (event) => {
      if (!props.visible) return
      
      switch (event.key) {
        case 'Escape':
          if (!props.preventClose) {
            handleClose()
          }
          break
        case 'Enter':
          handleRefresh()
          break
      }
    }

    // 设置自动刷新
    const setupAutoRefresh = () => {
      if (props.autoRefreshDelay > 0) {
        countdown.value = Math.ceil(props.autoRefreshDelay / 1000)
        
        autoRefreshTimer.value = setInterval(() => {
          countdown.value--
          if (countdown.value <= 0) {
            clearInterval(autoRefreshTimer.value)
            handleRefresh()
          }
        }, 1000)
      }
    }

    // 清理自动刷新
    const clearAutoRefresh = () => {
      if (autoRefreshTimer.value) {
        clearInterval(autoRefreshTimer.value)
        autoRefreshTimer.value = null
      }
    }

    // 监听可见性变化
    const handleVisibilityChange = () => {
      if (document.hidden) {
        clearAutoRefresh()
      } else if (props.visible && props.autoRefreshDelay > 0) {
        setupAutoRefresh()
      }
    }

    onMounted(() => {
      document.addEventListener('keydown', handleKeydown)
      document.addEventListener('visibilitychange', handleVisibilityChange)
      
      if (props.visible && props.autoRefreshDelay > 0) {
        setupAutoRefresh()
      }
    })

    onUnmounted(() => {
      document.removeEventListener('keydown', handleKeydown)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      clearAutoRefresh()
    })

    // 监听visible变化
    const unwatchVisible = computed(() => {
      if (props.visible && props.autoRefreshDelay > 0) {
        setupAutoRefresh()
      } else {
        clearAutoRefresh()
      }
    })

    return {
      isRefreshing,
      countdown,
      handleRefresh,
      handleLater,
      handleClose,
      handleOverlayClick
    }
  }
}
</script>

<style scoped>
.version-update-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

.version-update-dialog {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 400px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  animation: slideUp 0.3s ease-out;
}

.dialog-header {
  padding: 24px 24px 0;
  text-align: center;
}

.update-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.update-icon svg {
  width: 24px;
  height: 24px;
}

.dialog-content {
  padding: 20px 24px;
  text-align: center;
}

.dialog-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px;
}

.dialog-message {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin: 0 0 20px;
}

.version-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 8px;
}

.version-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.version-item:last-child {
  margin-bottom: 0;
}

.version-label {
  font-size: 13px;
  color: #666;
}

.version-value {
  font-size: 13px;
  font-weight: 500;
  color: #1a1a1a;
}

.dialog-actions {
  padding: 0 24px 24px;
  display: flex;
  gap: 12px;
}

.btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: #f1f3f4;
  color: #5f6368;
}

.btn-secondary:hover:not(:disabled) {
  background: #e8eaed;
}

.close-button {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 32px;
  height: 32px;
  border: none;
  background: #f1f3f4;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #5f6368;
  transition: all 0.2s ease;
}

.close-button:hover:not(:disabled) {
  background: #e8eaed;
  transform: scale(1.1);
}

.close-button svg {
  width: 16px;
  height: 16px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .version-update-dialog {
    background: #2d2d2d;
    color: #e0e0e0;
  }
  
  .dialog-title {
    color: #ffffff;
  }
  
  .dialog-message {
    color: #b0b0b0;
  }
  
  .version-info {
    background: #3a3a3a;
  }
  
  .version-label {
    color: #b0b0b0;
  }
  
  .version-value {
    color: #ffffff;
  }
  
  .btn-secondary {
    background: #404040;
    color: #e0e0e0;
  }
  
  .btn-secondary:hover:not(:disabled) {
    background: #4a4a4a;
  }
  
  .close-button {
    background: #404040;
    color: #e0e0e0;
  }
  
  .close-button:hover:not(:disabled) {
    background: #4a4a4a;
  }
}

/* 移动端适配 */
@media (max-width: 480px) {
  .version-update-dialog {
    margin: 16px;
    width: calc(100% - 32px);
  }
  
  .dialog-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
}
</style>
