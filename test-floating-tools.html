<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试悬浮工具栏功能</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .canvas-area {
            position: relative;
            width: 600px;
            height: 400px;
            background: #f0f0f0;
            border: 2px dashed #ccc;
            margin: 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: crosshair;
        }
        
        .canvas-area.drawing {
            background: #e8f4fd;
            border-color: #409eff;
        }
        
        /* 悬浮工具栏样式 */
        .floating-tools {
            position: absolute;
            bottom: 12px;
            left: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 8px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            z-index: 10;
            opacity: 1;
            visibility: visible;
            transition: opacity 0.2s ease, visibility 0.2s ease;
        }
        
        .floating-tools.hidden {
            opacity: 0;
            visibility: hidden;
        }
        
        .tool-icon {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #606266;
            background: transparent;
            border: 1px solid transparent;
            position: relative;
        }
        
        .tool-icon:hover:not(.disabled) {
            background: rgba(64, 158, 255, 0.1);
            color: #409eff;
            border-color: #409eff;
        }
        
        .tool-icon.active {
            background: #409eff;
            color: white;
        }
        
        .tool-icon.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        /* 工具提示样式 */
        .tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s ease, visibility 0.2s ease;
            margin-bottom: 5px;
            pointer-events: none;
        }
        
        .tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 4px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.8);
        }
        
        .tool-icon:hover .tooltip {
            opacity: 1;
            visibility: visible;
        }
        
        .status {
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d8ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #409eff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>悬浮工具栏功能测试</h1>
        
        <div class="instructions">
            <h3>测试说明：</h3>
            <ul>
                <li>在画布区域按住鼠标左键开始"涂抹"，悬浮工具栏会隐藏</li>
                <li>松开鼠标左键停止"涂抹"，悬浮工具栏会重新显示</li>
                <li>鼠标悬停在工具图标上会显示提示文本</li>
            </ul>
        </div>
        
        <div class="status" id="status">
            状态：未开始涂抹
        </div>
        
        <div class="canvas-area" id="canvasArea">
            <div style="color: #999;">点击并拖拽来模拟涂抹操作</div>
            
            <!-- 悬浮工具栏 -->
            <div class="floating-tools" id="floatingTools">
                <div class="tool-icon active">
                    <span>🖌️</span>
                    <div class="tooltip">画笔</div>
                </div>
                <div class="tool-icon">
                    <span>🧽</span>
                    <div class="tooltip">橡皮擦</div>
                </div>
                <div class="tool-icon">
                    <span>↶</span>
                    <div class="tooltip">撤销</div>
                </div>
                <div class="tool-icon">
                    <span>🗑️</span>
                    <div class="tooltip">清除</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvasArea = document.getElementById('canvasArea');
        const floatingTools = document.getElementById('floatingTools');
        const status = document.getElementById('status');
        
        let isDrawing = false;
        
        // 开始涂抹
        function startDrawing(e) {
            isDrawing = true;
            canvasArea.classList.add('drawing');
            floatingTools.classList.add('hidden');
            status.textContent = '状态：正在涂抹 - 悬浮工具栏已隐藏';
            status.style.background = '#fff2e8';
            status.style.color = '#e6a23c';
        }
        
        // 停止涂抹
        function stopDrawing(e) {
            if (isDrawing) {
                isDrawing = false;
                canvasArea.classList.remove('drawing');
                floatingTools.classList.remove('hidden');
                status.textContent = '状态：涂抹结束 - 悬浮工具栏已显示';
                status.style.background = '#f0f9ff';
                status.style.color = '#409eff';
            }
        }
        
        // 鼠标事件
        canvasArea.addEventListener('mousedown', startDrawing);
        canvasArea.addEventListener('mouseup', stopDrawing);
        canvasArea.addEventListener('mouseleave', stopDrawing);
        
        // 触摸事件（移动端）
        canvasArea.addEventListener('touchstart', (e) => {
            e.preventDefault();
            startDrawing(e);
        });
        
        canvasArea.addEventListener('touchend', (e) => {
            e.preventDefault();
            stopDrawing(e);
        });
        
        canvasArea.addEventListener('touchcancel', (e) => {
            e.preventDefault();
            stopDrawing(e);
        });
        
        // 工具图标点击事件
        document.querySelectorAll('.tool-icon').forEach((icon, index) => {
            icon.addEventListener('click', () => {
                // 移除其他图标的active状态
                document.querySelectorAll('.tool-icon').forEach(i => i.classList.remove('active'));
                // 添加当前图标的active状态
                icon.classList.add('active');
                
                const tools = ['画笔', '橡皮擦', '撤销', '清除'];
                status.textContent = `状态：选择了 ${tools[index]} 工具`;
                status.style.background = '#f0f9ff';
                status.style.color = '#409eff';
            });
        });
    </script>
</body>
</html>
