{"name": "vue-login-app", "private": true, "version": "1.0.0", "type": "module", "engines": {"node": "18.x"}, "scripts": {"dev": "vite", "build": "vite build --mode development-build", "build:dev": "vite build --mode development-build", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "preview": "vite preview"}, "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "@vueuse/core": "^10.4.1", "ali-oss": "^6.22.0", "ant-design-vue": "^4.2.6", "axios": "^1.8.4", "date-fns": "^2.30.0", "element-plus": "^2.3.14", "fluent-ffmpeg": "^2.1.3", "less": "^4.1.3", "markdown-it": "^14.1.0", "motion-v": "^1.0.0-beta.0", "swiper": "^11.2.6", "uuid": "^11.1.0", "vue": "^3.3.4", "vue-advanced-cropper": "^2.8.9", "vue-router": "^4.2.4"}, "devDependencies": {"@inspira-ui/plugins": "^0.0.1", "@types/uuid": "^10.0.0", "@vitejs/plugin-vue": "^4.3.4", "ant-design-x-vue": "^1.0.7", "autoprefixer": "^10.4.14", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "postcss": "^8.4.27", "rollup-plugin-visualizer": "^5.14.0", "tailwind-merge": "^1.14.0", "tailwindcss": "^3.3.3", "tailwindcss-animate": "^1.0.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.9", "vite-plugin-vue-devtools": "^0.5.1"}}