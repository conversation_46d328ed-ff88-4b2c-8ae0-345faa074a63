import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import VueDevTools from 'vite-plugin-vue-devtools'
import components from 'unplugin-vue-components/vite'
import { AntDesignXVueResolver } from 'ant-design-x-vue/resolver'

// 生成版本信息的插件
const generateVersionPlugin = () => {
  return {
    name: 'generate-version',
    generateBundle() {
      // 尝试多种方式获取版本号
      const getVersion = () => {
        // 1. 从环境变量获取
        if (process.env.npm_package_version && process.env.npm_package_version !== '0.0.0') {
          return process.env.npm_package_version;
        }

        // 2. 从自定义环境变量获取
        if (process.env.APP_VERSION) {
          return process.env.APP_VERSION;
        }

        // 3. 从 VITE_ 前缀的环境变量获取
        if (process.env.VITE_APP_VERSION) {
          return process.env.VITE_APP_VERSION;
        }

        // 4. 生成基于时间的版本号
        const timestamp = new Date().toISOString().replace(/[-:T]/g, '').slice(0, 14);
        return `1.0.${timestamp}`;
      };

      const version = getVersion();

      const versionInfo = {
        version: version,
        buildTime: Date.now(),
        hash: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15),
        gitCommit: process.env.VERCEL_GIT_COMMIT_SHA || process.env.GITHUB_SHA || process.env.GIT_COMMIT || 'unknown',
        buildEnv: process.env.NODE_ENV || 'development',
        deployTime: new Date().toISOString()
      }

      console.log('[Version Plugin] 环境变量检查:');
      console.log('  npm_package_version:', process.env.npm_package_version);
      console.log('  APP_VERSION:', process.env.APP_VERSION);
      console.log('  VITE_APP_VERSION:', process.env.VITE_APP_VERSION);
      console.log('  NODE_ENV:', process.env.NODE_ENV);
      console.log('[Version Plugin] 生成版本信息:', versionInfo);

      // 生成版本文件
      this.emitFile({
        type: 'asset',
        fileName: 'version.json',
        source: JSON.stringify(versionInfo, null, 2)
      })
    }
  }
}

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd())

  // 判断是否为生产类型环境（包括正式生产环境、测试环境和开发构建环境）
  const isBuildMode = mode === 'production' || mode === 'test' || mode === 'development-build'

  // 判断是否为开发服务器环境
  const isDevServer = command === 'serve' && mode === 'development'

  // 根据不同环境确定API地址
  let apiUrl = env.VITE_API_URL || 'https://dev-hub.weilitech.cn'
  
  return {
    plugins: [
      vue(),
      // 仅在开发环境启用VueDevTools
      isDevServer && VueDevTools(),
      components({
        resolvers: [AntDesignXVueResolver()]
      }),
      // 在构建时生成版本信息
      isBuildMode && generateVersionPlugin()
    ].filter(Boolean),
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    },
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true
        }
      }
    },
    // 优化依赖预构建
    optimizeDeps: {
      include: [
        'vue', 
        'vue-router', 
        'axios', 
        'element-plus/es/components/message/style/css',
        'element-plus/es/components/notification/style/css',
        '@vueuse/core'
      ],
      // 强制预构建这些依赖，提高启动速度
      force: true
    },
    // 优化开发服务器配置
    server: {
      host: '0.0.0.0',
      port: 3000,
      open: true,
      cors: true,  // 启用CORS
      // 优化HMR连接，加快热更新速度
      hmr: {
        overlay: false
      },
      proxy: isDevServer ? {
        '/api': {
          // target: 'https://dev-hub.weilitech.cn',
          target: 'https://dev.neodomain.cn',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
          secure: false  // 如果是https接口，设置为false
        }
      } : {}
    },
    // 为Vercel部署添加的配置
    build: {
      // 禁用sourcemap以减小构建大小（可选）
      sourcemap: false,
      // 启用CSS和JS代码分割
      cssCodeSplit: true,
      // 将大于4kb的依赖分割成chunks
      chunkSizeWarningLimit: 4000,
      // 优化分包策略
      rollupOptions: {
        output: {
          manualChunks: {
            'vue-vendor': ['vue', 'vue-router'],
            'element-plus-vendor': ['element-plus'],
            'ant-design': ['ant-design-vue'],
            'utils': ['@vueuse/core', 'axios', 'date-fns']
          }
        }
      },
      // 压缩配置，提高构建速度和体积
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: isBuildMode && mode === 'production',
          drop_debugger: isBuildMode
        }
      },
      // 启用关键CSS提取
      cssTarget: 'chrome80'
    },
    // 定义全局常量替换
    define: {
      // 注入API地址和模式到全局变量
      '__API_URL__': JSON.stringify(isBuildMode ? apiUrl : ''),
      '__API_MODE__': JSON.stringify(env.VITE_API_MODE || 'development')
    },
    // 配置发布环境的路由路径
    // base: isBuildMode ? '/agent/' : ''
    base: isBuildMode ? '' : ''
  }
})
